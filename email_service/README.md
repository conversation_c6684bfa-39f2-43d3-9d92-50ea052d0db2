# Email Service for Robot Framework Test Results

A simple Python email service to send Robot Framework test results via Gmail SMTP.

## Features

- Send email notifications with test results
- Attach test report files (HTML, XML)
- Simple configuration via JSON file
- Built-in email templates for test results
- Error handling and logging

## Setup

1. **Install Python dependencies** (if any additional packages needed):
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure email settings**:
   - Edit `config.json` with your email credentials
   - Make sure to use Gmail App Password instead of regular password

3. **Gmail App Password Setup**:
   - Enable 2-factor authentication on your Gmail account
   - Generate an App Password for this application
   - Use the App Password in the configuration

## Usage

### Basic Usage

```python
from email_sender import EmailSender

# Load configuration from file
email_sender = EmailSender(config_file='config.json')

# Send simple email
email_sender.send_email(
    subject="Test Results",
    body="All tests passed successfully!",
    attachments=["../results/report.html"]
)
```

### Send Test Results

```python
# Send formatted test results
email_sender.send_test_results(
    test_status="PASSED",
    test_details="All 5 tests completed successfully",
    attachments=["../results/report.html", "../results/log.html"]
)
```

### Configuration Options

The `config.json` file should contain:

```json
{
  "host": "smtp.gmail.com",
  "mail": "<EMAIL>",
  "password": "your-app-password",
  "port": 587,
  "mail_goal": ["<EMAIL>", "<EMAIL>"]
}
```

## Integration with Robot Framework

You can integrate this email service with your Robot Framework tests by:

1. **Adding to test teardown**:
   ```robot
   *** Test Teardown ***
   Run Keyword If Test Failed    Send Failure Email
   Run Keyword If Test Passed    Send Success Email
   ```

2. **Creating custom keywords**:
   ```robot
   *** Keywords ***
   Send Test Results Email
       ${result}=    Run Process    python    email_service/email_sender.py
       Log    Email sent: ${result.stdout}
   ```

## Security Notes

- Never commit your actual email password to version control
- Use environment variables or secure configuration management
- Consider using OAuth2 instead of App Passwords for production use

## Troubleshooting

1. **Authentication Error**: Make sure you're using Gmail App Password, not regular password
2. **Connection Error**: Check if your firewall allows SMTP connections on port 587
3. **File Not Found**: Ensure attachment file paths are correct relative to the script location

## Example Output

When running the script, you'll see output like:
```
Email sent <NAME_EMAIL>
Test results sent successfully!
```
