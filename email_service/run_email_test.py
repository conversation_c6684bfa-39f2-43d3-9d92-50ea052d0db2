#!/usr/bin/env python3
"""
Simple script to test email functionality
Run this script to send a test email with Robot Framework results
"""

import os
import sys
from email_sender import EmailSender


def check_result_files():
    """
    Check if Robot Framework result files exist
    
    Returns:
        list: List of existing result files
    """
    result_files = [
        "../results/report.html",
        "../results/log.html",
        "../results/output.xml"
    ]
    
    existing_files = []
    for file_path in result_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✓ Found result file: {file_path}")
        else:
            print(f"✗ Result file not found: {file_path}")
    
    return existing_files


def send_test_email():
    """
    Send a test email with available Robot Framework results
    """
    print("=" * 50)
    print("Robot Framework Email Service Test")
    print("=" * 50)
    
    try:
        # Load configuration
        config_file = "config.json"
        if not os.path.exists(config_file):
            print(f"Error: Configuration file {config_file} not found!")
            return False
        
        print(f"✓ Loading configuration from {config_file}")
        email_sender = EmailSender(config_file=config_file)
        
        # Check for result files
        print("\nChecking for Robot Framework result files...")
        result_files = check_result_files()
        
        # Prepare test details
        if result_files:
            test_details = f"""
Test execution completed successfully!

Available result files:
{chr(10).join(['- ' + os.path.basename(f) for f in result_files])}

This is a test email from the Robot Framework email service.
            """
        else:
            test_details = """
Test email sent successfully!

No Robot Framework result files found in ../results/ directory.
This is a test email from the Robot Framework email service.
            """
        
        # Send email
        print(f"\nSending email to: {email_sender.config['mail_goal']}")
        success = email_sender.send_test_results(
            test_status="TEST",
            test_details=test_details,
            attachments=result_files if result_files else None
        )
        
        if success:
            print("\n✓ Test email sent successfully!")
            print("Check your inbox for the test email.")
            return True
        else:
            print("\n✗ Failed to send test email.")
            return False
            
    except Exception as e:
        print(f"\n✗ Error: {str(e)}")
        return False


def main():
    """
    Main function
    """
    print("Starting email service test...")
    
    # Check if we're in the right directory
    if not os.path.exists("email_sender.py"):
        print("Error: Please run this script from the email_service directory")
        print("Usage: cd email_service && python run_email_test.py")
        sys.exit(1)
    
    success = send_test_email()
    
    if success:
        print("\n" + "=" * 50)
        print("Email service test completed successfully!")
        print("You can now integrate this with your Robot Framework tests.")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("Email service test failed!")
        print("Please check your configuration and try again.")
        print("=" * 50)
        sys.exit(1)


if __name__ == "__main__":
    main()
