"""
Email Service Package for Robot Framework Test Results

This package provides simple email functionality to send test results
via Gmail SMTP server.

Classes:
    EmailSender: Main class for sending emails with test results

Usage:
    from email_service import EmailSender
    
    sender = EmailSender(config_file='config.json')
    sender.send_test_results("PASSED", "All tests completed successfully")
"""

from .email_sender import EmailSender

__version__ = "1.0.0"
__author__ = "Robot Framework Email Service"
__all__ = ["EmailSender"]
