"""
Simple Email Service for sending test results
This module provides functionality to send email notifications with test results
"""

import smtplib
import json
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email import encoders
import os
from datetime import datetime


class EmailSender:
    """
    A simple email sender class for sending test results via Gmail SMTP
    """
    
    def __init__(self, config_file=None, config_dict=None):
        """
        Initialize EmailSender with configuration
        
        Args:
            config_file (str): Path to JSON configuration file
            config_dict (dict): Configuration dictionary
        """
        if config_file:
            self.config = self._load_config_from_file(config_file)
        elif config_dict:
            self.config = config_dict
        else:
            raise ValueError("Either config_file or config_dict must be provided")
    
    def _load_config_from_file(self, config_file):
        """
        Load email configuration from JSON file
        
        Args:
            config_file (str): Path to configuration file
            
        Returns:
            dict: Configuration dictionary
        """
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file {config_file} not found")
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in configuration file {config_file}")
    
    def send_email(self, subject, body, attachments=None):
        """
        Send email with test results
        
        Args:
            subject (str): Email subject
            body (str): Email body content
            attachments (list): List of file paths to attach
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Create message container
            msg = MIMEMultipart()
            msg['From'] = self.config['mail']
            msg['To'] = ', '.join(self.config['mail_goal'])
            msg['Subject'] = subject
            
            # Add body to email
            msg.attach(MIMEText(body, 'plain'))
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        self._attach_file(msg, file_path)
                    else:
                        print(f"Warning: Attachment file {file_path} not found")
            
            # Create SMTP session
            server = smtplib.SMTP(self.config['host'], self.config['port'])
            server.starttls()  # Enable security
            server.login(self.config['mail'], self.config['password'])
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.config['mail'], self.config['mail_goal'], text)
            server.quit()
            
            print(f"Email sent successfully to {', '.join(self.config['mail_goal'])}")
            return True
            
        except Exception as e:
            print(f"Error sending email: {str(e)}")
            return False
    
    def _attach_file(self, msg, file_path):
        """
        Attach a file to the email message
        
        Args:
            msg (MIMEMultipart): Email message object
            file_path (str): Path to file to attach
        """
        with open(file_path, "rb") as attachment:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(attachment.read())
        
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            f'attachment; filename= {os.path.basename(file_path)}'
        )
        msg.attach(part)
    
    def send_test_results(self, test_status="COMPLETED", test_details="", attachments=None):
        """
        Send test results with predefined format
        
        Args:
            test_status (str): Status of the test (PASSED, FAILED, COMPLETED)
            test_details (str): Additional test details
            attachments (list): List of result files to attach
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        subject = f"Robot Framework Test Results - {test_status} - {timestamp}"
        
        body = f"""
Test Execution Report
=====================

Execution Time: {timestamp}
Test Status: {test_status}

Test Details:
{test_details}

This is an automated email sent from Robot Framework test execution.

Best regards,
Automated Test System
        """
        
        return self.send_email(subject, body, attachments)


def main():
    """
    Main function to demonstrate email sending functionality
    """
    # Configuration from the provided JSON
    config = {
        "host": "smtp.gmail.com",
        "mail": "<EMAIL>",
        "password": "jzrpcwzvmnvbzxy",
        "port": 587,
        "mail_goal": ["<EMAIL>"]
    }
    
    # Create email sender instance
    email_sender = EmailSender(config_dict=config)
    
    # Send test results
    test_details = """
    - Login test: PASSED
    - Assessment test: PASSED
    - Program test: PASSED
    
    All tests completed successfully!
    """
    
    # You can attach result files if they exist
    result_files = [
        "../results/report.html",
        "../results/log.html"
    ]
    
    # Filter existing files
    existing_files = [f for f in result_files if os.path.exists(f)]
    
    success = email_sender.send_test_results(
        test_status="PASSED",
        test_details=test_details,
        attachments=existing_files
    )
    
    if success:
        print("Test results sent successfully!")
    else:
        print("Failed to send test results.")


if __name__ == "__main__":
    main()
