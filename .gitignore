# Robot Framework outputs
output.xml
log.html
report.html
*.log
*.xml
*.html
*.png
*.jpg
*.jpeg

# Ignore rebot merged outputs
output*.xml
log*.html
report*.html

# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Virtualenv
venv/
.env/
.envrc
.venv/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS generated files
.DS_Store
Thumbs.db

# Coverage reports
.coverage
coverage.xml
*.cover

# Robot temp folders
.robotframework-tmp/

# Pipenv, poetry
Pipfile.lock
poetry.lock

# Jupyter notebooks (if used)
.ipynb_checkpoints/
